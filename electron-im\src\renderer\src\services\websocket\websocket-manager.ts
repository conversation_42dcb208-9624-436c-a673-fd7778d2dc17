// WebSocket 业务协调层 - 协调服务层和状态层
import { APP_CONFIG } from '../../config'
import { WebSocketService } from './websocket-service'
import { useWebSocketStore } from '../../store/websocket'
import { notificationService, ErrorType, ErrorSeverity } from '../notificationService'
import { normalizeTimestamp } from '../../utils/time-utils'
import type {
  IWebSocketManager,
  WebSocketManagerEvents,
  WebSocketState,
  ConnectionInfo,
  TextMessage,
  ErrorMessage,
  ReceivedMessage,
  SendTextMessage
} from './types'
import { WebSocketState as WSState, MessageType } from './types'

export class WebSocketManager implements IWebSocketManager {
  private service: WebSocketService
  private store: ReturnType<typeof useWebSocketStore> | null = null
  private events: Partial<WebSocketManagerEvents> = {}

  // 重连相关
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 3000
  private reconnectTimeout: number | null = null
  private hasEverReconnected = false

  constructor() {
    this.service = new WebSocketService()
    this.setupServiceEventListeners()
  }

  // 延迟初始化 store
  private getStore(): ReturnType<typeof useWebSocketStore> {
    if (!this.store) {
      this.store = useWebSocketStore()
    }
    return this.store
  }

  // 事件监听器管理
  on<K extends keyof WebSocketManagerEvents>(event: K, callback: WebSocketManagerEvents[K]): void {
    this.events[event] = callback
  }

  off<K extends keyof WebSocketManagerEvents>(event: K): void {
    delete this.events[event]
  }

  private emit<K extends keyof WebSocketManagerEvents>(
    event: K,
    ...args: Parameters<NonNullable<WebSocketManagerEvents[K]>>
  ): void {
    const callback = this.events[event]
    if (callback) {
      ;(callback as any)(...args)
    }
  }

  // 设置服务层事件监听
  private setupServiceEventListeners(): void {
    this.service.on('onOpen', () => {
      this.handleConnectionOpen()
    })

    this.service.on('onClose', (event) => {
      this.handleConnectionClose(event)
    })

    this.service.on('onError', (event) => {
      this.handleConnectionError(event)
    })

    this.service.on('onMessage', (message) => {
      this.handleMessage(message)
    })
  }

  // 连接WebSocket
  async connect(token?: string): Promise<void> {
    try {
      // 如果已经连接，直接返回
      if (this.isConnected()) {
        return
      }

      // 设置连接中状态
      this.updateState(WSState.CONNECTING)

      // 连接服务
      await this.service.connect(token)
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.updateState(WSState.ERROR)

      const errorMsg: ErrorMessage = {
        code: 'CONNECTION_FAILED',
        message: error instanceof Error ? error.message : '连接失败'
      }
      this.emit('onError', errorMsg)

      // 尝试重连
      this.attemptReconnect()
      throw error
    }
  }

  // 断开连接
  disconnect(): void {
    this.clearReconnectTimeout()
    this.service.disconnect()
    this.updateState(WSState.DISCONNECTED)
    this.resetReconnectState()
  }

  // 手动重连
  reconnect(): void {
    console.log('WebSocketManager - 开始手动重连')
    this.clearReconnectTimeout()
    this.handleManualReconnect()
  }

  // 发送文本消息
  sendTextMessage(receiverId: string, content: string): boolean {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }

    const message: SendTextMessage = {
      type: MessageType.TEXT_MESSAGE,
      timestamp: Date.now(),
      textMessage: {
        receiverId,
        content
      }
    }

    console.log('📤 [WebSocketManager] 发送文本消息:', {
      receiverId,
      content,
      timestamp: message.timestamp,
      timestampDate: new Date(message.timestamp).toISOString(),
      messageFormat: message
    })

    return this.service.send(message)
  }

  // 检查连接状态
  isConnected(): boolean {
    return this.service.isConnected()
  }

  // 获取当前状态
  getState(): WebSocketState {
    return this.getStore().state
  }

  // 获取连接信息
  getConnectionInfo(): ConnectionInfo {
    return {
      isConnected: this.isConnected(),
      state: this.getState(),
      reconnectAttempts: this.reconnectAttempts,
      hasEverReconnected: this.hasEverReconnected,
      lastConnectedTime: this.getStore().connectionInfo?.lastConnectedTime,
      lastDisconnectedTime: this.getStore().connectionInfo?.lastDisconnectedTime
    }
  }

  // 清理资源
  cleanup(): void {
    this.clearReconnectTimeout()
    this.service.off('onOpen')
    this.service.off('onClose')
    this.service.off('onError')
    this.service.off('onMessage')
    this.events = {}
  }

  // 处理连接打开
  private handleConnectionOpen(): void {
    console.log('WebSocketManager - 连接已建立')

    // 更新状态
    this.updateState(WSState.CONNECTED)

    // 重置重连状态
    this.resetReconnectState()

    // 更新连接信息
    this.updateConnectionInfo({
      lastConnectedTime: new Date()
    })

    // 发射状态变化事件
    this.emit('onStateChange', WSState.CONNECTED)
  }

  // 处理连接关闭
  private handleConnectionClose(event: CloseEvent): void {
    console.log('WebSocketManager - 连接已关闭:', event.code, event.reason)

    // 更新状态
    this.updateState(WSState.DISCONNECTED)

    // 更新连接信息
    this.updateConnectionInfo({
      lastDisconnectedTime: new Date()
    })

    // 发射状态变化事件
    this.emit('onStateChange', WSState.DISCONNECTED)

    // 如果不是主动关闭，尝试重连
    if (event.code !== 1000) {
      this.attemptReconnect()
    }
  }

  // 处理连接错误
  private handleConnectionError(event: Event): void {
    console.error('WebSocketManager - 连接错误:', event)

    this.updateState(WSState.ERROR)

    const errorMsg: ErrorMessage = {
      code: 'CONNECTION_ERROR',
      message: '连接发生错误'
    }

    this.emit('onError', errorMsg)
    this.emit('onStateChange', WSState.ERROR)
  }

  // 处理接收到的消息
  private handleMessage(message: ReceivedMessage): void {
    try {
      console.log('WebSocketManager - 处理消息:', {
        type: message.type,
        messageId: message.messageId,
        timestamp: message.timestamp,
        hasTextMessage: !!message.textMessage,
        messageTypeValue: typeof message.type,
        messageTypeEnum: {
          HEARTBEAT: MessageType.HEARTBEAT,
          TEXT_MESSAGE: MessageType.TEXT_MESSAGE,
          ERROR: MessageType.ERROR
        }
      })

      switch (message.type) {
        case MessageType.TEXT_MESSAGE: {
          // 处理 timestamp 格式转换
          const normalizedTimestamp = normalizeTimestamp(message.timestamp)
          const textMessageTimestamp = message.textMessage?.timestamp
            ? normalizeTimestamp(message.textMessage.timestamp)
            : normalizedTimestamp

          console.log('📥 [WebSocketManager] 接收到文本消息:', {
            messageId: message.messageId,
            textMessage: message.textMessage,
            timestamp: message.timestamp,
            normalizedTimestamp,
            textMessageTimestamp,
            timestampDate: new Date(normalizedTimestamp).toISOString(),
            rawMessage: message
          })

          const textMsg: TextMessage = {
            id: message.messageId,
            senderId: message.textMessage?.senderId || '',
            receiverId: message.textMessage?.receiverId || '',
            content: message.textMessage?.content || '',
            timestamp: normalizedTimestamp,
            status: message.textMessage?.status,
            note: message.textMessage?.note,
            sender: message.textMessage?.sender
          }
          this.emit('onMessage', textMsg)
          break
        }

        case MessageType.ERROR: {
          const errorMsg: ErrorMessage = {
            code: message.code || 'UNKNOWN_ERROR',
            message: message.message || '未知错误'
          }
          this.emit('onError', errorMsg)
          break
        }

        case MessageType.HEARTBEAT: {
          const normalizedTimestamp = normalizeTimestamp(message.timestamp)
          console.log('💓 [WebSocketManager] 处理心跳消息:', {
            messageId: message.messageId,
            timestamp: message.timestamp,
            normalizedTimestamp,
            timestampDate: new Date(normalizedTimestamp).toISOString(),
            connectionHealthy: true
          })
          this.emit('onHeartbeat')
          break
        }

        default:
          console.warn('WebSocketManager - 未知消息类型:', message)
      }
    } catch (error) {
      console.error('WebSocketManager - 处理消息失败:', error)
    }
  }

  // 尝试重连
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocketManager - 重连次数已达上限')
      this.updateState(WSState.ERROR)

      // 只在达到最大重连次数时显示一次错误提示
      notificationService.handleError({
        type: ErrorType.WEBSOCKET,
        severity: ErrorSeverity.HIGH,
        message: '连接失败',
        details: `已尝试重连 ${this.maxReconnectAttempts} 次，请检查网络连接后点击重连按钮`,
        retryable: true,
        action: () => this.handleManualReconnect(),
        actionText: '重连'
      })
      return
    }

    this.reconnectAttempts++
    this.hasEverReconnected = true

    console.log(`WebSocketManager - 开始第 ${this.reconnectAttempts} 次重连`)

    this.updateState(WSState.RECONNECTING)
    this.emit('onStateChange', WSState.RECONNECTING)

    this.reconnectTimeout = window.setTimeout(async () => {
      try {
        const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY)
        await this.service.connect(token)
      } catch (error) {
        console.error(`WebSocketManager - 第 ${this.reconnectAttempts} 次重连失败:`, error)
        // 移除递归调用，改为延迟后再次尝试
        // 不在这里显示错误提示，避免重复提示
        this.scheduleNextReconnect()
      }
    }, this.reconnectDelay)
  }

  // 安排下一次重连
  private scheduleNextReconnect(): void {
    // 清除当前的重连定时器
    this.clearReconnectTimeout()

    // 延迟一段时间后再次尝试重连
    this.reconnectTimeout = window.setTimeout(() => {
      this.attemptReconnect()
    }, 1000) // 1秒后再次尝试
  }

  // 处理手动重连（重置重连次数）
  private handleManualReconnect(): void {
    console.log('WebSocketManager - 用户手动重连，重置重连次数')

    // 清除之前的通知
    notificationService.clearNotifications()

    // 重置重连次数
    this.reconnectAttempts = 0

    // 清除错误状态
    this.clearReconnectTimeout()

    // 开始重连
    this.attemptReconnect()
  }

  // 更新状态
  private updateState(newState: WebSocketState): void {
    const store = this.getStore()
    store.setState(newState)
    store.setReconnectAttempts(this.reconnectAttempts)
    store.setHasEverReconnected(this.hasEverReconnected)
  }

  // 更新连接信息
  private updateConnectionInfo(updates: Partial<ConnectionInfo>): void {
    const store = this.getStore()
    const currentInfo = store.connectionInfo || {
      isConnected: false,
      state: WSState.DISCONNECTED,
      reconnectAttempts: 0,
      hasEverReconnected: false
    }

    const newInfo: ConnectionInfo = {
      ...currentInfo,
      ...updates,
      isConnected: this.isConnected(),
      state: this.getState(),
      reconnectAttempts: this.reconnectAttempts,
      hasEverReconnected: this.hasEverReconnected
    }

    store.setConnectionInfo(newInfo)
  }

  // 重置重连状态
  private resetReconnectState(): void {
    this.reconnectAttempts = 0
    this.clearReconnectTimeout()
  }

  // 清除重连定时器
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
  }
}

// 创建单例实例
export const wsManager = new WebSocketManager()
